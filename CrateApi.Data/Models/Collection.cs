using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CrateApi.Data.Models.Interfaces;

namespace CrateApi.Data.Models;

public class Collection : IUserIdentifier, ICreatedDate, IUpdatedDate
{
    [Required]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int? Id { get; set; }

    [Required]
    [StringLength(256)]
    public string? Name { get; set; } = "";

    [Url]
    public string? Thumbnail { get; set; } = "";

    [Required]
    public DateTime Created { get; set; }

    [Required]
    public DateTime Updated { get; set; }

    [Required]
    public Guid? UserId { get; set; }
    public List<CollectionTrackMapping> CollectionTracks { get; set; } = new List<CollectionTrackMapping>();
}
