# /// script
# dependencies = [
#   "msal",
#   "requests",
#   "pyjwt[crypto]",
#   "pyperclip"
# ]
# ///

import msal
import json
import jwt
import requests
import pyperclip

# === Configuration ===
CLIENT_ID = "6d030d81-5d4a-42d1-a4c8-76c03b13fcc4"
TENANT_SUBDOMAIN = "cratenfc"
AUTHORITY = (
    f"https://{TENANT_SUBDOMAIN}.ciamlogin.com/{TENANT_SUBDOMAIN}.onmicrosoft.com"
)
SCOPES = ["api://cratenfc/access"]

"""
This script uses MSAL's ROPC (Resource Owner Password Credentials) flow to authenticate a local user
in an Entra External ID tenant and call protected APIs using a bearer token.

⚠️ ROPC flow does NOT support Microsoft personal accounts (e.g., @gmail.com, @outlook.com) or
federated/social logins (e.g., Google, Facebook, Apple). If you attempt to use an MSA (Microsoft Account),
you will encounter the following error:

    ValueError: Unable to find wstrust endpoint from MEX. This typically happens when attempting MSA accounts.

This error occurs because ROPC requires WS-Trust metadata, which is not available for Microsoft consumer identities.

🔐 To resolve:
- Use a test account created directly in your Entra tenant (e.g., <EMAIL>)
- Ensure it uses **local username + password login**, not federated or external providers

📖 More info:
https://github.com/AzureAD/microsoft-authentication-library-for-python/wiki/Username-Password-Authentication
"""

# === User credentials ===
USERNAME = "<EMAIL>"
PASSWORD = "CrateNFC2025"

PROTECTED_API_1 = "http://192.168.1.64:8000/api/v1/user/profile"
PROTECTED_API_2 = "http://192.168.1.64:8000/api/v1/user"



# === Helper: Build headers with API key + access token ===
def make_auth_headers(token: str) -> dict:
    return {"ApiKey": "crate2025", "Authorization": f"Bearer {token}"}


# === Authenticate via MSAL ROPC ===
app = msal.PublicClientApplication(client_id=CLIENT_ID, authority=AUTHORITY)

print(f"🔐 Authenticating {USERNAME}...\n")

result = app.acquire_token_by_username_password(
    username=USERNAME, password=PASSWORD, scopes=SCOPES
)

if "access_token" not in result:
    print("❌ Authentication failed:")
    print(result.get("error"))
    print(result.get("error_description"))
    exit(1)

print(json.dumps(result, indent=2, sort_keys=True))

print("\n🔎 Decoded ID payload:")
print(
    json.dumps(
        jwt.decode(result["id_token"], options={"verify_signature": False}), indent=2
    )
)

token = result["access_token"]
pyperclip.copy(token)
print(f"\nAccess Token (copied to clipboard):\n{token}")

print("\n🔎 Decoded JWT payload:")
print(json.dumps(jwt.decode(token, options={"verify_signature": False}), indent=2))

# === Call protected APIs ===
for api_url in [PROTECTED_API_1, PROTECTED_API_2]:
    print(f"\n📡 Calling: {api_url}")
    response = requests.get(api_url, headers=make_auth_headers(token))
    print(f"HTTP {response.status_code}")
    print(response.text)
