services:
  crateapi:
    image: ghcr.io/lilrobo/cratenfcapi-crateapi:dev-0.0.7
    ports:
      - 80:8000
      - 8000:8000
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8000
      - SSLMODE=Require
      - UnfurlServiceUrl=http://unfurlapi:8001
    depends_on:
      - unfurlapi

  unfurlapi:
    image: ghcr.io/lilrobo/cratenfcapi-unfurlapi:dev-0.0.7
    ports:
      - "8001:8001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8001
