using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CrateApi.Common.Dto.Request;
using CrateApi.Common.Dto.Response;
using CrateApi.Common.Extensions;
using CrateApi.Data.Models.Interfaces;
using CrateApi.Data.Models;
using CrateApi.Data;
using CrateApi.Services.Mappings;
using CrateApi.Services.Runtimes;
using LanguageExt.Common;
using LanguageExt.Traits;
using LanguageExt;
using Microsoft.Extensions.Logging;
using CrateApi.Common;
using CrateApi.Data.Context;
using Microsoft.EntityFrameworkCore;
using static LanguageExt.Prelude;
using CrateApi.Services.Logic;
using CrateApi.Services.Authentication;

namespace CrateApi.Services.Database;

public static class ContentService<M, RT>
    where RT :
        Has<M, CrateDbContext>,
        Has<M, IUserManager>,
        Has<M, ILogger<ApiRuntime>>
    where M :
        Monad<M>,
        Fallible<M>
{
    private static readonly Error InvalidValue = Expected.New(400, $"Invalid value");

    private static K<M, List<Content>> FindLatestContent(int start, int size) =>
        from context in Has<M, RT, CrateDbContext>.ask
        from um      in Has<M, RT, IUserManager>.ask
        from tracks  in IO.liftAsync(() =>
            context.Contents
                .Where(t => t.UserId == um.Current.UserId)
                .OrderByDescending(t => t.Updated)
                .Skip(start)
                .Take(size)
                .ToListAsync()
        )
        select tracks;

    public static K<M, UnfurledContentDto> Add(IContentUnfurled content) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Add), OL.Start)
        from dto           in
            content.Type switch {
                < (int)UnfurledContentType.Unsupported => AddTrackAndContent(content),
                _                                      => AddContent(content)
        }
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Add), OL.End)
        select dto;

    public static K<M, List<UnfurledContentDto>> Latest(int start = 0, int size = 20) =>
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Latest), OL.Start)
        from content       in FindLatestContent(start, size)
        from mapped        in M.Pure(ContentMapper.Instance.FromContent(content))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(Latest), OL.End)
        select mapped;

    private static K<M, UnfurledContentDto> AddContent(IContentUnfurled c) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddContent), OL.Start)
        from content       in M.Pure(ContentMapper.Instance.FromContentToContent(c))
        from _0            in DbTraits<M, RT>.AssignDbTraits(content)
        from addedContent  in context.AddAndSaveIO(content)
        from _1            in _l.LogInformationIO($"Added content (no track) for content with url: {c.Url}")
        from mapped        in M.Pure(ContentMapper.Instance.FromContent(addedContent))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddContent), OL.End)
        select mapped;

    private static K<M, UnfurledContentDto> AddTrackAndContent(IContentUnfurled c) =>
        from context       in Has<M, RT, CrateDbContext>.ask
        from um            in Has<M, RT, IUserManager>.ask
        from _l            in Has<M, RT, ILogger<ApiRuntime>>.ask
        from startLog      in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddTrackAndContent), OL.Start)
        from content       in M.Pure(ContentMapper.Instance.FromContentToContent(c))
        from track         in M.Pure(ContentMapper.Instance.FromContentToTrack(c))
        from _0            in DbTraits<M, RT>.AssignDbTraits(content)
        from _1            in DbTraits<M, RT>.AssignDbTraits(track)
        from addedTrack    in context.AddWithoutSaveIO(track)
        from addedContent  in context.AddWithoutSaveIO(content)
        from _3            in context.AddWithoutSaveIO(TrackContentMapping.Create(addedTrack, addedContent))
        from _4            in context.SaveChangesIO()
        from _5            in _l.LogInformationIO($"Added content and track for content with url: {c.Url}")
        from mapped        in M.Pure(ContentMapper.Instance.FromContent(addedContent))
        from endlog        in _l.LogMethod(nameof(ContentService<M, RT>), nameof(AddTrackAndContent), OL.End)
        select mapped;
}
