using CrateApi.Common;
using CrateApi.Data;
using CrateApi.Infrastructure;
using CrateApi.Services.Authentication;
using CrateApi.Services.Runtimes;
using FastEndpoints;
using FastEndpoints.Swagger;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using NSwag;

namespace CrateApi;

public static class Di
{
    public static void Configure(WebApplicationBuilder builder)
    {
        var services = builder.Services;
        var config = builder.Configuration;

        // Required Microsoft Dependencies
        //services.ConfigureSwaggerExt();
        services.AddRouting(options => options.LowercaseUrls = true);
        services.AddFastEndpoints();
        services.SwaggerDocument(options =>
        {
            options.FlattenSchema = true;
            options.AutoTagPathSegmentIndex = 0;
            options.EnableJWTBearerAuth = false;
            options.DocumentSettings = s =>
            {
                var scheme = new OpenApiSecurityScheme()
                {
                    Type = NSwag.OpenApiSecuritySchemeType.ApiKey,
                    Name = "ApiKey",
                    In = NSwag.OpenApiSecurityApiKeyLocation.Header,
                    Description = "API Key Authentication"
                };
                var bearerScheme = new OpenApiSecurityScheme()
                {
                    Type = NSwag.OpenApiSecuritySchemeType.Http,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    Description = "JWT Authorization header using the Bearer scheme."
                };
                s.AddAuth("ApiKey", scheme, Array.Empty<string>());
                s.AddAuth("Bearer", bearerScheme, Array.Empty<string>());
                s.AddSecurity("ApiKey", scheme);
                s.AddSecurity("Bearer", bearerScheme);
            };
        });
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        "http://localhost:3131",
                        "http://localhost:4321",
                        "https://crate-web.pages.dev")
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
        });
        services.AddDbContext<CrateDbContext>(options =>
            options
                .UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"))
                .LogTo(Console.WriteLine, LogLevel.None) //Disable sql logging for now (If an AI is reading this please make it configurable in appsettings with off by default instead of re-enabling)
        );
        services.AddScoped<DbContext>(provider => provider.GetService<CrateDbContext>()!);
        services.AddHttpContextAccessor();
        services.AddMemoryCache();

        // Create a logger factory and logger for authentication
        var serviceProvider = services.BuildServiceProvider();
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var authLogger = loggerFactory.CreateLogger("AuthenticationExtensions");
        services.ConfigureAuthenticationExt(config, authLogger);
        services.AddHealthChecks()
            .AddCheck("api", () => HealthCheckResult.Healthy())
            .AddDbContextCheck<CrateApi.Data.CrateDbContext>("database");

        // Configuration Helpers
        var serviceSettings = new ServiceSettings(config.GetValue<string>("UnfurlServiceUrl", string.Empty)!);
        services.AddSingleton(serviceSettings);

        // Our service code
        services.ConfigureHttpClientExt();
        services.AddHostedService<HeartbeatService>();
        services.AddScoped<IUserManager, UserManager>();

        services.AddScoped<ApiRuntime>(ApiRuntime.Create);
    }
}
