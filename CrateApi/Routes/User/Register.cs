using CrateApi.Data;
using CrateApi.Infrastructure;
using CrateApi.Models;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;

namespace CrateApi.Routes.UserEndpoints;

public class Register(CrateDbContext context) : Endpoint<CreateUserRequest>
{
    public override void Configure()
    {
        Post("user/register");
        AllowAnonymous();
        PreProcessor<ApiKeyRequirement<CreateUserRequest>>();
        Description(d => d
            .WithTags("User"));
    }

    public override async Task HandleAsync(CreateUserRequest req, CancellationToken ct)
    {
        var user = new CrateApi.Data.Models.User
        {
            Email    = req.Email,
            Username = req.Username,
            Password = req.Password,
        };

        var dbUser = await context.Users.AddAsync(user);
        await context.SaveChangesAsync();
        await SendOkAsync(dbUser.Entity);
    }
}
